import { is } from 'bpmn-js/lib/util/ModelUtil';

/**
 * 自定义属性提供者 - 基于最新的bpmn-js-properties-panel API
 */
export default function CustomPropertiesProvider(
  propertiesPanel: any,
  translate: any,
  modeling: any
) {

  // 实现getGroups方法
  this.getGroups = function(element: any) {
    console.log('CustomPropertiesProvider.getGroups called for element:', element);
    console.log('Element type:', element?.type);
    console.log('Element businessObject:', element?.businessObject);

    const groups: any[] = [];

    // 只为流程元素显示属性
    if (is(element, 'bpmn:Process')) {
      console.log('Creating group for Process element');

      const generalGroup = {
        id: 'general',
        label: translate('常规信息'),
        entries: []
      };

      // 添加ID字段
      generalGroup.entries.push({
        id: 'process-id',
        label: translate('ID'),
        modelProperty: 'id',
        widget: 'textField',
        get: function(element: any) {
          const value = element.businessObject.id || '';
          console.log('Getting ID value:', value);
          return { id: value };
        },
        set: function(element: any, values: any) {
          console.log('Setting ID value:', values.id);
          modeling.updateProperties(element, { id: values.id });
        },
        validate: function(element: any, values: any) {
          if (!values.id || values.id.trim() === '') {
            return { id: translate('ID为必填项') };
          }
          return {};
        }
      });

      // 添加名称字段
      generalGroup.entries.push({
        id: 'process-name',
        label: translate('名称'),
        modelProperty: 'name',
        widget: 'textField',
        get: function(element: any) {
          const value = element.businessObject.name || '';
          console.log('Getting name value:', value);
          return { name: value };
        },
        set: function(element: any, values: any) {
          console.log('Setting name value:', values.name);
          modeling.updateProperties(element, { name: values.name });
        },
        validate: function(element: any, values: any) {
          if (!values.name || values.name.trim() === '') {
            return { name: translate('名称为必填项') };
          }
          return {};
        }
      });

      groups.push(generalGroup);
      console.log('Created general group:', generalGroup);
    }

    console.log('Returning groups:', groups);
    return groups;
  };
}

CustomPropertiesProvider.$inject = [
  'propertiesPanel',
  'translate', 
  'modeling'
];
