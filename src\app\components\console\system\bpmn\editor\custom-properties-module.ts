import { is } from 'bpmn-js/lib/util/ModelUtil';

/**
 * 自定义属性模块
 * 用于完全替换默认的属性面板内容
 */

// 自定义属性提供者
function CustomPropertiesProvider(
  propertiesPanel: any,
  translate: any,
  modeling: any
) {

  // 设置高优先级，确保覆盖默认提供者
  this.getGroups = function(element: any) {
    const groups: any[] = [];

    // 为流程(Process)元素添加自定义属性组
    if (is(element, 'bpmn:Process')) {
      groups.push(createGeneralInfoGroup(element, translate, modeling));
    }

    return groups;
  };

  // 设置高优先级
  this.getGroups.priority = 1000;
}

CustomPropertiesProvider.$inject = [
  'propertiesPanel',
  'translate',
  'modeling'
];

/**
 * 创建常规信息属性组
 */
function createGeneralInfoGroup(element: any, translate: any, modeling: any) {
  return {
    id: 'general-info',
    label: translate('常规信息'),
    entries: [
      // ID字段
      {
        id: 'process-code',
        label: translate('ID'),
        modelProperty: 'code',
        widget: 'textField',
        get: function(element: any) {
          const businessObject = element.businessObject;
          return {
            code: businessObject.get('code') || businessObject.get('id') || ''
          };
        },
        set: function(element: any, values: any) {
          modeling.updateProperties(element, {
            code: values.code,
            id: values.code // 同时更新id属性
          });
        },
        validate: function(element: any, values: any) {
          const code = values.code;
          if (!code || code.trim() === '') {
            return { code: translate('ID为必填项') };
          }
          return {};
        }
      },
      // 名称字段
      {
        id: 'process-name',
        label: translate('名称'),
        modelProperty: 'name',
        widget: 'textField',
        get: function(element: any) {
          const businessObject = element.businessObject;
          return {
            name: businessObject.get('name') || ''
          };
        },
        set: function(element: any, values: any) {
          modeling.updateProperties(element, {
            name: values.name
          });
        },
        validate: function(element: any, values: any) {
          const name = values.name;
          if (!name || name.trim() === '') {
            return { name: translate('名称为必填项') };
          }
          return {};
        }
      }
    ]
  };
}

// 导出自定义属性模块
export default {
  __init__: ['customPropertiesProvider'],
  customPropertiesProvider: ['type', CustomPropertiesProvider]
};
