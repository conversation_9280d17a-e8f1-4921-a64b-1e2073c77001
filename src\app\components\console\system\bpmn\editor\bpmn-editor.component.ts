import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js相关模块
import BpmnModeler from 'bpmn-js/lib/Modeler';
import {
    BpmnPropertiesPanelModule,
    BpmnPropertiesProviderModule
} from 'bpmn-js-properties-panel';
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json';

// 导入中文翻译模块
import TranslateModule from 'src/app/utils/bpmn-translate';

@Component({
    selector: 'app-bpmn-editor',
    templateUrl: './bpmn-editor.component.html',
    styleUrls: ['./bpmn-editor.component.less']
})
export class BpmnEditorComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;
    @ViewChild('propertiesPanel', { static: false }) propertiesPanel!: ElementRef;

    private bpmnModeler: any;
    public processId: string | null = null;
    public processData: any = null;
    public loading = false;
    public saving = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.processId = this.route.snapshot.paramMap.get('id');

        if (this.processId) {
            this.loadProcessData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initBpmnModeler();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.bpmnModeler) {
            this.bpmnModeler.destroy();
        }
    }

    // 加载流程数据
    private async loadProcessData(): Promise<void> {
        if (!this.processId) return;

        try {
            this.loading = true;
            const response = await this.bpmnService.getById(Number(this.processId)).then(res => {
                if (response.success) {
                    this.processData = response.data;

                    // 如果BPMN建模器已初始化，导入XML
                    if (this.bpmnModeler) {
                        this.importBpmnXml(this.processData.bpmnXml || this.defaultBpmnXml);
                    }
                } else {
                    this.msg.error('加载流程数据失败');
                }
            }).catch(err => {
                this.msg.error('获取流程列表失败');
                console.error(err);
            });
        } catch (error) {
            console.error('加载流程数据失败:', error);
            this.msg.error('加载流程数据失败');
        } finally {
            this.loading = false;
        }
    }

    // 初始化BPMN建模器
    private initBpmnModeler(): void {
        if (!this.bpmnContainer || !this.propertiesPanel) {
            return;
        }

        try {
            // 创建BPMN建模器实例
            this.bpmnModeler = new BpmnModeler({
                container: this.bpmnContainer.nativeElement,
                propertiesPanel: {
                    parent: this.propertiesPanel.nativeElement
                },
                additionalModules: [
                    BpmnPropertiesPanelModule,
                    BpmnPropertiesProviderModule,
                    TranslateModule
                ],
                moddleExtensions: {
                    camunda: camundaModdleDescriptor
                }
                // 移除keyboard配置，新版本bpmn-js会自动处理键盘绑定
            });

            // 导入BPMN XML
            const xmlToImport = this.processData?.bpmnXml || this.defaultBpmnXml;
            this.importBpmnXml(xmlToImport);

            // 监听建模器事件
            this.setupEventListeners();

            // 定制属性面板
            this.customizePropertiesPanel();

        } catch (error) {
            console.error('初始化BPMN建模器失败:', error);
            this.msg.error('初始化BPMN建模器失败');
        }
    }

    // 设置事件监听器
    private setupEventListeners(): void {
        if (!this.bpmnModeler) return;

        // 监听元素变化事件
        this.bpmnModeler.on('commandStack.changed', () => {
            // 可以在这里添加自动保存逻辑
        });

        // 监听元素选择事件
        this.bpmnModeler.on('selection.changed', (event: any) => {
            console.log('Selection changed:', event);
            this.onElementSelected(event);
        });
    }

    // 定制属性面板
    private customizePropertiesPanel(): void {
        if (!this.bpmnModeler) return;

        try {
            // 获取属性面板实例
            const propertiesPanel = this.bpmnModeler.get('propertiesPanel');
            console.log('Properties panel instance:', propertiesPanel);

            // 监听属性面板更新事件
            this.bpmnModeler.on('propertiesPanel.updated', (event: any) => {
                console.log('Properties panel updated:', event);
                this.customizePropertiesPanelContent();
            });

        } catch (error) {
            console.error('Error customizing properties panel:', error);
        }
    }

    // 处理元素选择事件
    private onElementSelected(event: any): void {
        const element = event.newSelection[0];
        if (element && element.type === 'bpmn:Process') {
            console.log('Process element selected:', element);
            setTimeout(() => {
                this.customizePropertiesPanelContent();
            }, 100);
        }
    }

    // 定制属性面板内容
    private customizePropertiesPanelContent(): void {
        try {
            const propertiesPanelElement = this.propertiesPanel.nativeElement;
            console.log('Properties panel element:', propertiesPanelElement);

            // 查找并隐藏不需要的属性组
            const groupsToHide = [
                'CamundaPlatform__HistoryCleanup',
                'CamundaPlatform__TasklistConfiguration',
                'CamundaPlatform__CandidateStarter',
                'CamundaPlatform__ExternalTask',
                'CamundaPlatform__ExecutionJob',
                'CamundaPlatform__ExecutionListener',
                'CamundaPlatform__ExtensionProperties'
            ];

            groupsToHide.forEach(groupId => {
                const groupElement = propertiesPanelElement.querySelector(`[data-group="${groupId}"]`);
                if (groupElement) {
                    (groupElement as HTMLElement).style.display = 'none';
                    console.log(`Hidden group: ${groupId}`);
                }
            });

            // 添加自定义字段
            this.addCustomFields();

        } catch (error) {
            console.error('Error customizing properties panel content:', error);
        }
    }

    // 添加自定义字段
    private addCustomFields(): void {
        try {
            const propertiesPanelElement = this.propertiesPanel.nativeElement;

            // 查找通用属性组
            const generalGroup = propertiesPanelElement.querySelector('[data-group="general"]');
            if (generalGroup) {
                console.log('Found general group, adding custom fields');

                // 创建自定义字段HTML
                const customFieldsHtml = `
                    <div class="bio-properties-panel-entry" data-entry="custom-id">
                        <label class="bio-properties-panel-label">ID</label>
                        <input type="text" class="bio-properties-panel-input" id="custom-process-id" placeholder="请输入ID">
                    </div>
                    <div class="bio-properties-panel-entry" data-entry="custom-name">
                        <label class="bio-properties-panel-label">名称</label>
                        <input type="text" class="bio-properties-panel-input" id="custom-process-name" placeholder="请输入名称">
                    </div>
                `;

                // 插入自定义字段
                generalGroup.insertAdjacentHTML('beforeend', customFieldsHtml);

                // 绑定事件
                this.bindCustomFieldEvents();
            }
        } catch (error) {
            console.error('Error adding custom fields:', error);
        }
    }

    // 绑定自定义字段事件
    private bindCustomFieldEvents(): void {
        const idInput = this.propertiesPanel.nativeElement.querySelector('#custom-process-id') as HTMLInputElement;
        const nameInput = this.propertiesPanel.nativeElement.querySelector('#custom-process-name') as HTMLInputElement;

        if (idInput && nameInput) {
            // 获取当前选中的元素
            const selection = this.bpmnModeler.get('selection');
            const modeling = this.bpmnModeler.get('modeling');
            const selectedElement = selection.get()[0];

            if (selectedElement && selectedElement.type === 'bpmn:Process') {
                // 设置当前值
                idInput.value = selectedElement.businessObject.id || '';
                nameInput.value = selectedElement.businessObject.name || '';

                // 绑定输入事件
                idInput.addEventListener('input', (event) => {
                    const target = event.target as HTMLInputElement;
                    modeling.updateProperties(selectedElement, { id: target.value });
                });

                nameInput.addEventListener('input', (event) => {
                    const target = event.target as HTMLInputElement;
                    modeling.updateProperties(selectedElement, { name: target.value });
                });
            }
        }
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        try {
            await this.bpmnModeler.importXML(xml);

            // 自适应画布大小
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');

            // 自动选择流程元素以显示属性面板
            setTimeout(() => {
                const elementRegistry = this.bpmnModeler.get('elementRegistry');
                const selection = this.bpmnModeler.get('selection');

                // 查找流程元素
                const processElement = elementRegistry.find(element => element.type === 'bpmn:Process');
                if (processElement) {
                    console.log('Found process element, selecting it:', processElement);
                    selection.select(processElement);
                } else {
                    // 如果没有找到流程元素，选择根元素
                    const rootElement = canvas.getRootElement();
                    console.log('Selecting root element:', rootElement);
                    selection.select(rootElement);
                }
            }, 100);

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败');
        }
    }

    // 保存流程
    async save(): Promise<void> {
        try {
            this.saving = true;

            // 获取BPMN XML
            const result = await this.bpmnModeler.saveXML({ format: true });
            const bpmnXml = result.xml;

            // 获取SVG图像
            const svgResult = await this.bpmnModeler.saveSVG();
            const svgImage = svgResult.svg;

            // 准备保存数据
            const saveData = {
                id: this.processId,
                bpmnXml: bpmnXml,
                svgImage: svgImage
            };

            // 调用服务保存
            const response = await this.bpmnService.saveProcess(saveData);

            if (response.success) {
                this.msg.success('保存成功');
            } else {
                this.msg.error(response.message || '保存失败');
            }

        } catch (error) {
            console.error('保存流程失败:', error);
            this.msg.error('保存流程失败');
        } finally {
            this.saving = false;
        }
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/bpmn']);
    }

    // 缩放到适合视口
    zoomToFit(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');
        }
    }

    // 重置缩放
    zoomReset(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom(1);
        }
    }
}